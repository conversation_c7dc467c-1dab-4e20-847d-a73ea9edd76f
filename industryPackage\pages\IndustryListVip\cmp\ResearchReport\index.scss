.wrap {
  // 移除min-height，让内容自然撑开
  background: #ffffff;

  // 针对scroll-view的优化
  will-change: transform;
  overflow: hidden;

  .tip {
    width: 100%;
    height: 86rpx;
    background: #f7f7f7;
    font-size: 28rpx;
    font-weight: 400;
    color: #74798c;
    display: flex;
    align-items: center;
    padding-left: 24rpx;

    .name {
      font-weight: 600;
      font-size: 28rpx;
      color: #20263a;
      margin-left: 8rpx;
    }

    .num {
      font-weight: 600;
      font-size: 28rpx;
      color: #e72410;
      padding: 0 8rpx;
    }
  }

  .list {
    // 确保列表在scroll-view中正常显示
    background: #ffffff;

    .item {
      display: flex;
      background: #ffffff;
      padding: 32rpx 24rpx;
      position: relative;
      // 优化触摸反馈
      transition: background-color 0.2s ease;

      // 1px底部边框
      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 24rpx;
        width: 702rpx;
        height: 1rpx;
        background: #eee;
        transform: scaleY(0.5);
      }

      &:last-child::after {
        display: none;
      }

      // 点击态效果（适配scroll-view）
      &:active {
        background-color: #f8f9fa;
      }

      .image-container {
        flex-shrink: 0;
        margin-right: 20rpx;
        position: relative;

        .report-image {
          width: 104rpx;
          height: 136rpx;
        }

        .txt {
          position: absolute;
          width: 104rpx;
          height: 28rpx;
          background: linear-gradient(270deg, #fd9331 0%, #ffb93e 100%);
          font-weight: 600;
          font-size: 20rpx;
          color: #ffffff;
          top: 84rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .content {
        flex: 1;
        display: flex;
        flex-direction: column;

        .title {
          font-weight: 400;
          font-size: 28rpx;
          color: #20263a;
          margin-bottom: 16rpx;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 1.4;
        }

        .info-row {
          margin-bottom: 16rpx;
          // 整体最多2行，超出截取
          max-height: calc(36rpx * 2 + 8rpx); // 2行的高度 + 间距
          overflow: hidden;

          // 使用flex布局，允许换行
          display: flex;
          flex-wrap: wrap;
          gap: 8rpx 16rpx;
          align-items: center;

          .file-info {
            font-weight: 400;
            font-size: 24rpx;
            color: #9b9eac;
            border-radius: 4rpx;
            border: 1rpx solid #a0a5ba;
            padding: 0 10rpx;
            height: 36rpx;
            display: flex;
            align-items: center;
            flex-shrink: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200rpx;
          }

          .tags {
            display: contents; // 让tags的子元素直接参与父级的flex布局

            .tag {
              height: 36rpx;
              padding: 0 10rpx;
              background: rgba(74, 184, 255, 0.1);
              border-radius: 4rpx;
              font-weight: 400;
              font-size: 24rpx;
              color: #4ab8ff;
              max-width: 200rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              display: flex;
              align-items: center;
              flex-shrink: 0;

              &:nth-of-type(2) {
                background: rgba(253, 147, 49, 0.1);
                color: #fd9331;
              }
            }
          }
        }

        .bottom-row {
          display: flex;
          align-items: center;

          .organization {
            font-weight: 400;
            font-size: 24rpx;
            color: #74798c;
            margin-right: 40rpx;
            // 最多2行
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .date {
            font-weight: 400;
            font-size: 24rpx;
            color: #9b9eac;
            flex-shrink: 0;
          }
        }
      }
    }

    // hover效果类
    .item-hover {
      background-color: #f8f9fa !important;
    }
  }
  // 确保最后一个元素有足够的底部间距
  .list .item:last-child {
    margin-bottom: 24rpx;
  }
}
