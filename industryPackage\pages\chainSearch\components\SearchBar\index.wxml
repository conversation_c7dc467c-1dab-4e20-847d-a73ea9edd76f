<view class="search-container">
  <!-- 搜索输入框 -->
  <view class="search-input-wrapper" bindtap="onFaSearch">
    <view class="search-icon">
      <image
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/in_search.png"
        mode="aspectFit"
      ></image>
    </view>
    <input
      class="search-input"
      type="text"
      placeholder="{{placeholder}}"
      placeholder-class="placeholder"
      bindblur="onBlur"
      value="{{inputVal}}"
      focus="{{inputShowed}}"
      bindinput="onInput"
      bindconfirm="onConfirm"
      confirm-type="search"
      disabled="{{isDisable}}"
      clearable="{{inputVal.length > 0}}"
      bindfocus="onFocus"
    />
  </view>

  <!-- 筛选按钮 -->
  <view class="filter {{dropdownVisible && 'active'}}" bindtap="toggleDropdown">
    {{ selectedFilter.chain_name}}
  </view>
</view>

<!-- 下拉筛选列表 -->
<view
  class="dropdown-overlay"
  wx:if="{{dropdownVisible}}"
  catchtap="closeDropdown"
>
  <view class="filter-dropdown" catchtap="stopPropagation">
    <scroll-view scroll-y class="filter_list">
      <view
        class="filter_item {{selectedFilter.chain_code === item.chain_code && 'active'}}"
        wx:for="{{filterOptions}}"
        wx:key="chain_code"
        data-item="{{item}}"
        catchtap="selectFilter"
      >
        {{item.chain_name}}
      </view>
    </scroll-view>
  </view>
</view>
