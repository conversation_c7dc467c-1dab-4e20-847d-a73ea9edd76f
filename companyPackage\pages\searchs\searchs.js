import constant from '../../../utils/constant';
import {home} from '../../../service/api';
import {getHeight} from '../../../utils/height';
import {preventActive} from '../../../utils/util';
import {debounce, formatDate, handleSearchHight} from '../../../utils/formate';
import {hasPrivile} from '../../../utils/route';
import {collect} from '../../../utils/mixin/collect';
const app = getApp();
Page({
  data: {
    isLogin: false,
    // 搜索相关
    inputShowed: false, //是否聚焦
    ent_name: '', //搜索值
    ents_name: '',
    historyList: [],
    browsingHistory: [], //浏览历史
    timer: null,
    // 弹窗相关
    popType: '',
    showVisible: false, //是否显示弹窗
    title: '',
    content: '',
    cancelBtnText: '取消',
    confirmBtnText: '删除',
    // 浏览历史高度
    scrollHeight: 'auto',
    filtrateHeight: 'auto',
    statusBarHeight: 'auto',
    // 筛选
    dropDownMenuTitle: ['全国', '全部行业', '更多筛选'],
    // 卡片 下拉加载更多
    cardHeight: 'auto',
    // 请求相关
    bazaarParms: {
      page_index: 1, //偏移量
      page_size: 10 //每页多少条
    },
    bazaarlist: [], //获取列表
    bazaarIsFlag: true, //节流 true允许
    bazaarHasData: true, //  是否还有数据
    bazaarIsNull: false, // list长度是否为0
    bazaarIsTriggered: false, // 下拉刷新状态

    // 弹窗相关
    popType: '',
    showVisible: false, //是否显示弹窗
    title: '',
    content: '',
    cancelBtnText: '取消',
    confirmBtnText: '删除',
    cnacelEnt: '', //缓存取消收藏企业id
    popIndex: 0, //缓存取消收藏企业下标
    // 联系方式
    showContact: false,
    contactList: [], //联系方式列表
    // 地址
    showAddress: false,
    addmarkers: [],
    locationTxt: '',
    location: {
      lat: '',
      lon: ''
    },
    locationMap: {},
    // 高级搜索参数
    heightParams: {},

    activeEntId: '', // 当前点击企业的id
    entUnlogin: false // 是否是在未登录的情况点击了企业标题
  },
  onShow() {
    this.scrollH();
    const {login} = app.globalData;
    this.setData(
      {
        isLogin: login
      },
      () => {
        // login && app.showLoading('加载中')
        login &&
          Promise.all([this.getSearchHisList(), this.getBevHisList()])
            .then(res => {
              // wx.hideLoading()
            })
            .catch(err => {
              // wx.hideLoading()
            });
      }
    );
  },
  onLoad(options) {
    this.setData({
      inputShowed: true
    });
  },
  // input相关--onblur后面真机看是否保留
  onConfirm: function (e) {
    let keyword = e.detail.value;
    const {isLogin} = this.data;
    if (keyword) {
      isLogin && this.addHistorySearch(keyword);
      // 发请求跟线上同步---这里有个问题，游客的时候不会发请求，登录后和游客这里是不一样的
      isLogin &&
        home.addHistory({
          keyword,
          model_type: 'ENTERPRISE'
        });
    }
  },
  addHistorySearch(value) {
    let historySearch = wx.getStorageSync(constant.HistorySearch) || [];

    let has = historySearch.includes(value);
    if (has) {
      let index = historySearch.findIndex(item => item == value);
      if (index == 0) return;
      historySearch.splice(index, 1);
    }
    let len = historySearch.length;
    if (len >= 10) {
      historySearch.pop();
    }
    historySearch.unshift(value);
    wx.setStorageSync(constant.HistorySearch, historySearch);
    this.setData({
      historyList: historySearch
    });
  },
  onClear() {
    this.unLocked();
    this.setData({
      ent_name: '',
      ents_name: ''
    });
    this.inputQuest('suspend');
  },
  init() {
    this.setData({
      ent_name: '',
      ents_name: '',
      inputShowed: false
    });
  },
  isBlur() {
    this.setData({
      inputShowed: true
    });
  },
  onBlur() {
    // 拿到ent_name --传入最近搜索历史
    const ent_nameue = this.data.ent_name;
    if (ent_nameue.trim().length > 0) {
      this.addHistorySearch(ent_nameue);
    } else {
      this.setData({
        inputShowed: false
      });
    }
  },
  onInput: debounce(function ([...e]) {
    let keyword = e[0].detail.value;
    if (keyword || keyword == '') {
      this.setData({
        ent_name: keyword
      });
      app.showLoading('加载中');
      this.inputQuest();
    }
  }, 2200),
  goBack() {
    this.unLocked();
    this.init();
    app.route(this, null, 'navigateBack');
  },
  unLocked() {
    wx.hideKeyboard();
    this.setData({
      loading: false,
      inputShowed: false
    });
  },
  inputQuest(suspend) {
    //处理input的数据到请求参数里面--过滤
    const {ent_name, bazaarParms} = this.data;
    bazaarParms['ent_name'] = ent_name;
    bazaarParms.page_index = 1;
    this.setData({
      bazaarParms,
      bazaarlist: []
    });
    // !suspend &&
    this.initGetList(() => {
      wx.hideLoading();
      //完成之后--浏览历史
    });
  },
  // 点击最近搜索
  historyTap(e) {
    const keyword = e.target.dataset['item'];
    this.setData(
      {
        inputShowed: true,
        ent_name: keyword,
        ents_name: keyword
      },
      () => this.inputQuest()
    );
  },
  // 点击删除图标
  handleIcon(e) {
    const type = e.currentTarget.dataset['index'];
    const that = this;
    switch (type) {
      // 情况最近搜索
      case 'a':
        wx.showModal({
          title: '删除搜索',
          content: '确定要删除最近搜索?',
          success: function (res) {
            if (res.confirm) {
              wx.removeStorageSync(constant.HistorySearch);
              that.setData({
                showVisible: false,
                historyList: []
              });
              // 发请求同步
              home.clearHistory('ENTERPRISE');
            }
          }
        });
        break;
      case 'b': //清空浏览历史
        wx.showModal({
          title: '删除浏览历史',
          content: '确定要删除最近搜索?',
          success: function (res) {
            if (res.confirm) {
              // 发送请求 --成功删除历史
              home.detBevHis('ENTERPRISE').then(res => {
                app.showToast('删除成功!');
                that.setData({
                  browsingHistory: []
                });
              });
            }
          }
        });
        break;
      default:
        break;
    }
  },
  onClose() {
    this.setData({
      showVisible: false
    });
  },
  async getSearchHisList() {
    //获取搜索历史 列表
    let arr = [];
    home
      .getHistory('ENTERPRISE')
      .then(res => {
        if (res && res.length > 0) {
          arr = res.map(item => item.key_word);
          arr = [...new Set(arr)];
        }
        wx.setStorageSync(constant.HistorySearch, arr);
        this.setData({
          historyList: arr
        });
      })
      .catch(err => {
        console.log(err);
      });
  },
  async getBevHisList() {
    //获取浏览历史 列表
    let arr = [];
    home
      .getBevHis('ENTERPRISE')
      .then(res => {
        if (res.length > 0) {
          arr = res.map(item => {
            let {enterprise_name, enterprise_id, create_time} = item;
            create_time = formatDate(create_time, 'MM-dd');
            return {
              enterprise_name,
              enterprise_id,
              create_time
            };
          });
          arr = arr.slice(0, 10);
        }
        this.setData({
          browsingHistory: arr
        });
      })
      .catch(err => {
        console.log(err);
      });
  },
  //获取小米列表
  initGetList(callback) {
    const that = this;
    let {bazaarlist, bazaarHasData, bazaarParms} = that.data;
    that.setData({
      bazaarIsNull: false,
      bazaarIsFlag: false,
      bazaarHasData: true
    });
    // 计算卡片高度
    this.cardHeight();
    //处理请求参数
    const tempParams = {
      ...bazaarParms,
      current_page: bazaarParms.page_index
    };
    delete bazaarParms.page_size;

    home
      .portrait(tempParams)
      .then(res => {
        let items = res?.list || [];
        let count = res?.total || [];
        let ary = [];
        if (items.length < bazaarParms.page_size) bazaarHasData = false;
        ary = items.map((item, index) => {
          item.register_date = formatDate(item.register_date, 'yyyy-MM-dd');
          item.heightKey = bazaarParms['ent_name'];
          return item;
        });
        ary = handleSearchHight(ary, 'ent_name', that.data.ent_name);
        that.setData(
          {
            bazaarlist: bazaarlist.concat(ary),
            bazaarHasData,
            bazaarIsFlag: true,
            count: count || 0
          },
          () => {
            if (!that.data.bazaarlist.length)
              that.setData({
                bazaarIsNull: true,
                bazaarHasData: true
              });
          }
        );
        callback && callback();
      })
      .catch(err => {
        callback && callback();
        this.setData({
          bazaarIsNull: true
        });
        app.showToast(err?.data?.message || '获取数据失败!请稍后再试');
      });
  },
  // 下拉刷新
  bazaarRefresher() {
    const that = this;
    app.showLoading('加载中');
    wx.showNavigationBarLoading();
    let {bazaarParms} = that.data;
    let obj = {
      ...bazaarParms,
      page_index: 1,
      page_size: 10
    };
    that.setData(
      {
        bazaarParms: obj,
        bazaarlist: [],
        bazaarHasData: true
      },
      () =>
        that.initGetList(() => {
          wx.hideLoading();
          that.setData({
            bazaarIsTriggered: false
          });
          wx.hideNavigationBarLoading();
        })
    );
  },
  //加载更多
  async bazaarloadMore() {
    if (!app.isLogin()) {
      this.setData({
        count: 5
      });
      return;
    }
    // 判断vip类型 主要是针对普通用户
    if (this.data.bazaarlist.length >= 50) {
      let permission = await hasPrivile({
        packageType: true
      });
      this.setData({
        permission
      });
      if (permission == '普通VIP') return;
    }
    let {bazaarParms, bazaarHasData, bazaarIsFlag} = this.data;
    if (!bazaarHasData) return;
    if (!bazaarIsFlag) return; //节流
    bazaarParms.page_index += 1;
    this.setData(
      {
        bazaarParms
      },
      () => this.initGetList()
    );
  },
  // 筛选
  onFlitter(e) {
    let {dropDownMenuTitle, bazaarParms, bazaarlist, ent_name} = this.data;
    // console.log(this.data);
    const obj = e.detail;
    dropDownMenuTitle[0] = obj.name1;
    dropDownMenuTitle[1] = obj.name2;
    let isFilter = obj.isFilter;
    delete obj['name1'];
    delete obj['name2'];
    delete obj['isFilter'];
    // 因为obj有部分删除了 不能直接...bazaarParms
    let oldbazaarParms = JSON.parse(JSON.stringify(bazaarParms));
    bazaarParms = {
      page_index: 1,
      page_size: 10,
      ...obj,
      ent_name
    };
    let temObj = {};
    if (isFilter) {
      temObj = bazaarParms;
    } else {
      temObj = {
        ...oldbazaarParms,
        // 新格式数据映射到老格式API字段
        industry_code_list: obj['industry_code_list'],
        area_code_list: obj['area_code_list'],
        ent_name,
        page_index: 1,
        page_size: 10
      };
    }
    bazaarlist = [];
    this.setData(
      {
        bazaarParms: temObj,
        bazaarlist,
        dropDownMenuTitle
      },
      () => {
        app.showLoading('加载中...');
        // console.log(this.data.bazaarParms)
        this.initGetList(() => wx.hideLoading());
      }
    );
  },
  //----------
  // 动态获取页面高度
  cardHeight() {
    var that = this;
    getHeight(that, ['.drop-menu'], data => {
      const {screeHeight, res} = data;
      let h1 = res[0]?.top;
      let h2 = res[0]?.height;
      this.setData({
        cardHeight: screeHeight - h1 - h2
      });
    });
  },
  scrollH() {
    var that = this;
    getHeight(
      that,
      ['.searchs', '.search_a', '.drop-menu', '.his_title'],
      data => {
        const {screeHeight, res, statusBarHeight} = data;
        let h1 = res[0]?.height || 0;
        let h2 = res[1]?.height || 0;
        let h4 = res[3]?.height || 0;
        // 处理search外剩余的高度
        let filtrateHeight = screeHeight - h1;
        // 浏览历史的滚动高度
        let scrollHeight = screeHeight - h1 - h2 - h4;
        that.setData({
          scrollHeight: scrollHeight,
          filtrateHeight: filtrateHeight,
          statusBarHeight: statusBarHeight
        });
      }
    );
  },
  // 卡片点击回调
  goto(e) {
    const {item} = e.currentTarget.dataset;
    console.log('点击跳转到详情');
  },
  async onCard(data) {
    let that = this;
    preventActive(this, async () => {
      // 地址  "site"  联系方式 "relation" 官网 "official" 收藏 "collect" 四个字段对呀四个方法逻辑-具体见设计图
      const type = data.detail.type;
      const comDetail = data.detail.data;
      // console.log(index)
      // 处理收藏
      // return
      if (type == 'collect') {
        collect(that, comDetail, 'bazaarlist');
      } else if (type == 'relation') {
        this.setData({
          activeEntId: comDetail.ent_id,
          showContact: true
        });
        // let contactRes = await common.contact(comDetail.ent_id, encodeURI("types=1,2"))
        // console.log("联系方式res", contactRes);
        // if (contactRes.length > 0) {
        //   this.setData({
        //     contactList: contactRes,
        //     showContact: true
        //   })
        // } else {
        //   app.showToast('暂无联系方式', 'none', 1300)
        //   this.setData({
        //     contactList: [],
        //     showContact: false
        //   })
        // }
      } else if (type === 'site') {
        this.setData({
          location: {
            lat: +comDetail.location.lat,
            lon: +comDetail.location.lon
          },
          locationTxt: comDetail.register_address,
          addmarkers: [
            {
              id: 1,
              latitude: +comDetail.location.lat,
              longitude: +comDetail.location.lon,
              iconPath:
                'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png',
              width: 20,
              height: 20
            }
          ],
          showAddress: true,
          locationMap: {
            latitude: +comDetail.location.lat, //维度
            longitude: +comDetail.location.lon, //经度
            name: comDetail.register_address, //目的地定位名称
            scale: 15, //缩放比例
            address: comDetail.register_address //导航详细地址
          }
        });
      }
    });
  },
  goMap() {
    const {locationMap} = this.data;
    wx.openLocation(locationMap);
  },
  onCloseContact() {
    this.setData({
      showContact: false
    });
  },
  onCloseAddress() {
    this.setData({
      showAddress: false
    });
  },
  makeCall(e) {
    const item = e.target.dataset['item'] || e.currentTarget.dataset['item'];
    // console.log(item.contact_data)
    wx.makePhoneCall({
      phoneNumber: item.contact_data
    });
  },
  handleTit({detail}) {
    console.log('detali', detail);
    const {isLogin} = this.data;
    this.setData({
      entUnlogin: !isLogin
    });
    if (isLogin) {
      let {ent_name: enterprise_name, ent_id: enterprise_id, logo} = detail;
      home.addBevHis({
        enterprise_name,
        enterprise_id,
        behavior_history_mode: 'INDEX_PAGE',
        enterprise_log: logo ? logo : '-',
        model_type: 'ENTERPRISE'
      }); //新增浏览历史
    }
  },
  // 去详情页面
  goDetail(e) {
    let {enterprise_id} = e.currentTarget.dataset.item;
    const url = encodeURIComponent(
      `https://reporth5.handidit.com?entId=${enterprise_id}`
    );
    // const url = encodeURIComponent(`http://***************:8080/?entId=${enterprise_id}`)
    // const url = encodeURIComponent(`http://j-h5-report.ihdwork.com?entId=${enterprise_id}`)
    app.route(this, `/subPackage/pages/webs/index?url=${url}`);
  },
  vipPop(val) {
    this.setData({
      vipVisible: val
    });
  },
  login() {
    const url = '/companyPackage/pages/searchs/searchs';
    app.route(this, `/pages/login/login?url=${url}`);
  }
});
