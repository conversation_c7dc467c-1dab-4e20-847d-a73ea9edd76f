/**
 * 简化版下拉刷新和上拉加载更多组件
 * 参考 searchs 页面的实现方式
 */
const app = getApp();
Component({
  options: {
    multipleSlots: true
  },

  externalClasses: [
    'custom-wrapper-class',
    'custom-container-class',
    'custom-loading-class',
    'custom-empty-class'
  ],

  properties: {
    // 容器高度
    containerHeight: {
      type: Number,
      value: 600
    },

    // 空状态配置
    emptyText: {
      type: String,
      value: '暂无数据'
    },
    emptyTip: {
      type: String,
      value: '下拉刷新试试'
    },

    // API 请求配置
    requestUrl: {
      type: null, // 支持 Function
      value: ''
    },
    requestParams: {
      type: Object,
      value: {}
    },
    autoLoad: {
      type: Boolean,
      value: true
    },
    requestType: {
      type: String,
      value: 'currentPage' //pageIndex
    }
  },

  data: {
    // 列表数据
    list: [],
    currentPage: 1,
    totalCount: 0,
    hasMore: true,
    isLoading: false,
    isEmpty: false,
    isTriggered: false, // 下拉刷新状态
    isInitialLoading: false, // 初始加载状态

    // 写死的配置
    pageSize: 10,
    listField: 'list',
    totalField: 'total',

    // 内部状态
    _initialized: false // 组件是否已初始化
  },

  methods: {
    /**
     * 发起 API 请求
     */
    async makeRequest(isRefresh = false) {
      const {requestUrl, requestParams} = this.properties;
      const {currentPage, pageSize} = this.data;
      if (!requestUrl || typeof requestUrl !== 'function') {
        console.error('requestUrl 必须是一个函数');
        return;
      }

      const page = isRefresh ? 1 : currentPage;
      const params = {
        ...requestParams,
        current_page: page,
        page_size: pageSize
      };
      if (this.properties.requestType == 'pageIndex') {
        params.page_index = page;
        delete params.current_page;
      }
      try {
        const response = await requestUrl(params);

        return this.processResponse(response, isRefresh);
      } catch (error) {
        console.error('API请求失败:', error);
        // 将错误抛给外面
        // this.triggerEvent('error', error);
        throw error;
      }
    },

    /**
     * 处理响应数据
     */
    processResponse(response, isRefresh) {
      const {listField, totalField, pageSize} = this.data;
      const {list: currentList} = this.data;

      // 提取数据
      const newList = response[listField] || [];
      const total = response[totalField] || 0;

      // 合并数据
      const finalList = isRefresh ? newList : [...currentList, ...newList];
      const hasMore = newList.length >= pageSize && total > pageSize;
      const isEmpty = finalList.length === 0;

      // console.log('数据处理结果:', {
      //   原始数据: newList.length,
      //   最终列表: finalList.length,
      //   hasMore,
      //   isEmpty
      // });

      // 更新状态
      this.setData({
        list: finalList,
        currentPage: isRefresh ? 2 : this.data.currentPage + 1,
        totalCount: total,
        hasMore,
        isEmpty,
        isLoading: false,
        isTriggered: false,
        isInitialLoading: false // 隐藏初始加载状态
      });

      // 触发数据更新事件
      this.triggerEvent('datachange', {
        list: finalList,
        total,
        hasMore,
        isEmpty,
        isRefresh,
        originalResponse: response
      });

      return {list: finalList, total, hasMore, isEmpty};
    },

    /**
     * 下拉刷新
     */
    async onRefresh() {
      this.setData({isTriggered: true});

      try {
        await this.makeRequest(true);
      } catch (error) {
        this.setData({
          isTriggered: false,
          isEmpty: true
        });
      }
    },

    /**
     * 上拉加载更多
     */
    async onLoadMore() {
      const {hasMore, isLoading} = this.data;

      console.log('onLoadMore 被调用:', {hasMore, isLoading});

      if (!hasMore || isLoading) {
        console.log('不执行加载更多，但仍然触发滚动到底部事件');
        return;
      }

      this.setData({isLoading: true});
      try {
        await this.makeRequest(false);
      } catch (error) {
        console.error('加载更多失败:', error);
        // app.showToast(error.data.message || '请求数据失败!');
        this.setData({
          isLoading: false,
          hasMore: false
        });
      }
    },

    /**
     * 滚动到底部事件（传递给父组件）
     */
    onScrollToLower(e) {
      console.log(33333333333);
      console.log('refresh-scroll: 滚动到底部事件触发', {
        listLength: this.data.list.length,
        hasMore: this.data.hasMore
      });

      // 保持原有逻辑：处理加载更多
      this.onLoadMore();

      // 新增：传递滚动到底部事件给父组件
      this.triggerEvent('scrolltolower', {
        ...e.detail,
        currentListLength: this.data.list.length,
        hasMore: this.data.hasMore
      });
    },

    /**
     * 滚动事件（传递给父组件）
     */
    onScroll(e) {
      // 传递滚动事件给父组件
      this.triggerEvent('scroll', e.detail);
    },

    /**
     * 测试方法：手动触发滚动到底部事件
     */
    testScrollToLower() {
      console.log('手动触发滚动到底部事件');
      this.triggerEvent('scrolltolower', {
        currentListLength: this.data.list.length,
        hasMore: this.data.hasMore,
        manual: true // 标记这是手动触发的
      });
    },

    /**
     * 初始加载数据
     */
    async loadData() {
      if (!this.properties.requestUrl) {
        console.warn('未配置requestUrl，跳过自动加载');
        return;
      }

      // 显示初始加载状态
      this.setData({
        currentPage: 1,
        list: [],
        hasMore: true,
        isEmpty: false,
        isInitialLoading: true
      });

      try {
        await this.makeRequest(true);
      } catch (error) {
        console.error('初始加载失败:', error);
        this.setData({
          isEmpty: true,
          isInitialLoading: false
        });
      }
    },

    /**
     * 重新加载数据
     */
    async reload() {
      await this.loadData();
    }
  },

  lifetimes: {
    attached() {
      // 标记组件已初始化
      this.setData({
        _initialized: true
      });

      // 自动加载数据
      if (this.properties.autoLoad) {
        setTimeout(() => {
          this.loadData();
        }, 100);
      }
    }
  },

  observers: {
    requestParams: function (newParams, oldParams) {
      // 只有在组件已经初始化且参数真正变化时才重新加载
      if (
        this.data._initialized &&
        newParams &&
        JSON.stringify(newParams) !== JSON.stringify(oldParams)
      ) {
        // console.log('参数变化，重新加载数据:', newParams);
        if (this.properties.requestUrl && this.properties.autoLoad) {
          this.reload();
        }
      }
    }
  }
});
