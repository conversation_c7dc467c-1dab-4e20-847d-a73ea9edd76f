<!--industryPackage/pages/businessMapList/index.wxml-->
<view class="pages">
  <view class="page_head">
    <DropDownMenu
      dropDownMenuTitle="{{dropDownMenuTitle}}"
      dropDownMenuConfig="{{dropDownMenuConfig}}"
      class="drop-menu"
      bindsubmit="onFlitter"
      bindvip="vipPop"
      useIndustrySingleSelect="{{useIndustrySingleSelect}}"
      chain_code="{{chain_code}}"
      externalFilterState="{{externalFilterState}}"
    />
  </view>

  <!-- 内容 支持全国/省/市/区所有层级  这里逻辑后续要和后端确定 现在下拉弹窗 全国，省/市 有些是1级 有些是2级 这个到时候看如何区分-->
  <scroll-view scroll-y class="card-box" style="height: {{computedHeight}}px;">
    <!-- 地图 -->
    <TitLayout isLine title="{{dropDownMenuTitle[0]}}企业总量">
      <view slot="left" class="tit_left"> {{totalCompanyCount}}家 </view>
      <view slot="content" class="map_cont">
        <Map
          region="{{mapRegion}}"
          heatMapData="{{heatMapData}}"
          markPoints="{{markPoints}}"
        ></Map>
      </view>
    </TitLayout>
    <!-- 全国省排行 -->
    <TitLayout
      isLine
      title="省份排行"
      wx:if="{{ mapRegion.code === '1000000'}}"
    >
      <view slot="content">
        <EchartsHorizontalBar
          chartData="{{provinceBarData}}"
        ></EchartsHorizontalBar>
      </view>
    </TitLayout>
    <!-- 全国城市排行 -->
    <TitLayout
      isLine
      title="城市排行"
      wx:if="{{ mapRegion.code === '1000000' || (mapRegion.level === '1'&&!isMunicipality)}}"
    >
      <view slot="content">
        <EchartsHorizontalBar
          chartData="{{cityBarData}}"
          barColor="{{['#29B296','#30CEAE']}}"
          isRepeat="{{true}}"
        ></EchartsHorizontalBar>
      </view>
    </TitLayout>
    <TitLayout
      isLine
      title="区县排行"
      wx:if="{{mapRegion.level === '2' || isMunicipality}}"
    >
      <view slot="content">
        <EchartsHorizontalBar
          chartData="{{cityBarData}}"
          barColor="{{['#F58319','#FFB26B']}}"
          isRepeat="{{true}}"
        ></EchartsHorizontalBar>
      </view>
    </TitLayout>
    <!-- 重点企业 -->
    <EnterpriseList
      isLine
      bind:itemClick="onEnterpriseItemClick"
      wx:if="{{ mapRegion.code != '1000000' }}"
      source="{{industryData}}"
    />

    <view style="height: 90rpx; border: 1px solid transparent;"></view>
  </scroll-view>
  <!-- 浮窗-企业列表 -->
  <view class="fixed_btn" bindtap="onShowEnterpriseList">企业列表</view>
  <!-- 弹出企业列表 -->
  <van-popup
    show="{{ showPop }}"
    bind:close="onClose"
    position="right"
    custom-style="height: 100vh; width: 90vw;"
  >
    <view class="popup-content" catchtouchmove="onPopupTouchMove">
      <view
        class="popup-close-btn {{hasActivePopup ? 'under-mask' : ''}}"
        catchtap="onClose"
      >
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/close.png"
        />
      </view>
      <BusinessListComponent
        wx:if="{{showPop}}"
        dropDownMenuConfig="{{isIndustryMap? ['region']: ['region', 'filter']}}"
        fixedTitle="{{popupFixedTitle}}"
        showMapMode="{{false}}"
        popupMode="{{true}}"
        externalFilterState="{{externalFilterState}}"
        isIndustryMap="{{isIndustryMap ? true : false}}"
        dropDownMenuTitle="{{isIndustryMap ? ['全国'] :  ['全国', '更多筛选']}}"
        category="{{category}}"
        bindpopupStateChange="onPopupStateChange"
      />
    </view>
  </van-popup>
  <!-- VIP弹窗 -->
  <VipPop visible="{{vipVisible}}" bindclose="vipPop"></VipPop>
</view>
