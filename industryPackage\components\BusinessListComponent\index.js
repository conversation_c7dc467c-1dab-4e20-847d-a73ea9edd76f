import {chain} from '../../../service/api';
import {preventActive} from '../../../utils/util.js';
import {getHeight} from '../../../utils/height.js';
import {collect} from '../../../utils/mixin/collect';
import Toast from '@vant/weapp/toast/toast.js';

const app = getApp();
Component({
  properties: {
    // 固定文本标题（动态，如"产业类型"）
    fixedTitle: {
      type: String,
      value: '',
      observer(newVal) {
        // 当 fixedTitle 变化时，重新更新下拉菜单配置
        this.updateDropDownConfig();
      }
    },
    chain_name: {
      type: String,
      value: ''
    },
    // 是否显示地图模式按钮（动态）
    showMapMode: {
      type: Boolean,
      value: true
    },

    // 弹窗模式（新增）
    popupMode: {
      type: Boolean,
      value: false,
      observer(newVal, oldVal) {
        // 当弹窗模式变化时，重新计算高度
        if (newVal !== oldVal && this.data.initialized) {
          setTimeout(() => {
            this.handleHeight();
          }, 200);
        }
      }
    },

    // 弹窗顶部占位高度（rpx单位，用于兼容不同的弹窗布局）
    popupTopOffset: {
      type: Number,
      value: 0 // 默认0，表示没有额外的顶部占位
    },
    // 下拉菜单配置（主要配置项）
    dropDownMenuConfig: {
      type: Array,
      value: ['region', 'industry', 'filter'], // 默认配置：地区、产业链、更多筛选
      observer() {
        this.updateDropDownConfig();
      }
    },

    // 外部筛选状态（用于回显外部的筛选条件）
    externalFilterState: {
      type: Object,
      value: null,
      observer(newVal) {
        if (newVal && this.data.initialized) {
          this.applyExternalFilterState(newVal);
        }
      }
    },
    category: {
      type: String,
      value: '' //区分热点和经典产业链
    },
    // 是否是产业图谱--渲染得样式是不一样的
    isIndustryMap: {
      type: Boolean,
      value: false,
      observer(newVal) {
        if (newVal) {
          this.setData({
            isCheckVip: 'masonryVip'
          });
        }
      }
    },
    //产业图谱是否购买-进到这里默认是购买的
    purchased: {
      type: Boolean,
      value: true
    },
    isCheckVip: {
      // 这里应该有三种 1.vip 2.masonryVip 3.不需要vip
      type: String,
      value: ''
    },

    // 是否使用产业链专用选择器
    useIndustrySingleSelect: {
      type: Boolean,
      value: false
    },
    // 产业链代码（用于IndustrySingleSelect获取数据）
    chain_code: {
      type: String,
      value: ''
    }
  },

  data: {
    company_num: 0,
    requestData: [],
    initialized: false, // 标记组件是否已初始化

    // 写死的参数
    requestFunction: chain.chainDetail, // 请求函数
    requestParams: {
      industrial_list: []
    }, // 请求参数

    industrial_list: [], // 产业链列表

    // 联系方式
    showContact: false,
    // 地址
    showAddress: false,
    addmarkers: [],
    locationTxt: '',
    location: {
      lat: '',
      lon: ''
    },
    locationMap: {},
    activeEntId: '', // 当前点击企业的id
    isLogin: false,
    computedHeight: 600, // 列表容器高度
    vipVisible: false, // VIP弹窗显示状态
    masonryVipVisible: false, // 未购买弹窗

    // 滚动相关状态
    isScrolledToBottom: false, // 是否滚动到底部
    showBottomVipPopup: false, // 是否显示底部VIP弹窗（基于滚动状态）

    // 动态下拉菜单配置
    computedDropDownMenuConfig: [],
    dropDownMenuTitle: [], // 根据配置自动生成

    // 存储外部筛选状态
    externalFilterState: null,

    // 是否有活跃的弹窗
    hasActivePopup: false
  },

  observers: {
    // 监听所有弹窗状态变化
    'showContact, showAddress, vipVisible, masonryVipVisible': function () {
      this.updatePopupState();
    },
    // 监听购买状态和滚动状态，控制底部VIP弹窗显示
    'purchased, isScrolledToBottom, requestData': function () {
      this.updateBottomVipPopupState();
    }
  },

  lifetimes: {
    attached() {
      this.setData({
        isLogin: app.isLogin()
      });
      // 先更新配置，再初始化数据
      this.updateDropDownConfig();
    },
    ready() {
      // 标记初始化完成
      this.setData({
        initialized: true
      });

      // 如果有外部筛选状态，先应用它
      if (this.properties.externalFilterState) {
        this.applyExternalFilterState(this.properties.externalFilterState);
      }

      // 手动触发组件加载数据
      this.selectComponent('#refresh-scroll').loadData();
      this.handleHeight();

      // 初始化弹窗状态
      this.updatePopupState();

      // 设置定时器定期检查 DropDownMenu 状态变化
      this.setupDropDownMenuWatcher();
    },

    detached() {
      // 组件销毁时清理定时器
      this.cleanupWatcher();
    }
  },

  methods: {
    // 更新弹窗状态
    updatePopupState() {
      // 获取 DropDownMenu 组件的弹窗状态
      const dropDownMenu = this.selectComponent('.drop-menu');
      let dropDownPopupActive = false;

      if (dropDownMenu && dropDownMenu.data) {
        const {district_open, source_open, filter_open, four_open} =
          dropDownMenu.data;
        dropDownPopupActive =
          district_open || source_open || filter_open || four_open;
      }

      // 获取当前组件的弹窗状态
      const {showContact, showAddress, vipVisible, masonryVipVisible} =
        this.data;
      const componentPopupActive =
        showContact || showAddress || vipVisible || masonryVipVisible;

      // 计算是否有活跃弹窗
      const hasActivePopup = dropDownPopupActive || componentPopupActive;

      // 如果状态发生变化，更新数据并通知外层
      if (this.data.hasActivePopup !== hasActivePopup) {
        this.setData({
          hasActivePopup
        });

        // 通知外层页面弹窗状态变化
        this.triggerEvent('popupStateChange', {
          hasActivePopup
        });
      }
    },

    // 设置 DropDownMenu 状态监听器
    setupDropDownMenuWatcher() {
      // 使用定时器定期检查 DropDownMenu 状态
      this.dropDownMenuWatcher = setInterval(() => {
        this.updatePopupState();
      }, 100); // 每100ms检查一次
    },

    // 清理监听器
    cleanupWatcher() {
      if (this.dropDownMenuWatcher) {
        clearInterval(this.dropDownMenuWatcher);
        this.dropDownMenuWatcher = null;
      }
    },

    // 更新下拉菜单配置
    updateDropDownConfig() {
      const {dropDownMenuConfig, fixedTitle} = this.properties;

      // 根据配置生成对应的标题
      const titleMap = {
        region: '全国',
        industry: fixedTitle || '产业类型', // 使用外部传入的 fixedTitle
        filter: '更多筛选'
      };

      const finalTitles = dropDownMenuConfig.map(
        config => titleMap[config] || '更多筛选'
      );
      this.setData({
        computedDropDownMenuConfig: dropDownMenuConfig,
        dropDownMenuTitle: finalTitles
      });
    },
    // 处理组件数据变化
    onDataChange(e) {
      let {list, total, hasMore} = e.detail;
      const {purchased} = this.data;

      console.log('数据变化事件:', {
        purchased,
        originalListLength: list?.length,
        total,
        hasMore
      });

      // 未购买产业链图谱，只显示前10条数据
      if (!purchased) {
        list = (list || []).slice(0, 10);
        console.log('未购买，截取到10条数据');

        // 不要立即阻断请求，让滚动事件能正常触发
        // 只在真正滚动到底部时才阻断
      }

      this.setData({
        requestData: list,
        company_num: total || 0
      });

      // 向父组件传递数据变化事件
      this.triggerEvent('datachange', {
        list,
        total: total || 0
      });
    },

    // 阻断未购买用户的后续请求
    blockFurtherRequests() {
      const refreshScrollComponent = this.selectComponent('#refresh-scroll');
      if (refreshScrollComponent) {
        console.log('阻断后续请求，设置 hasMore: false');
        // 设置组件状态，阻止进一步的加载更多
        refreshScrollComponent.setData({
          hasMore: false
        });
      }
    },

    // 地图导航
    goMap() {
      const {locationMap} = this.data;
      wx.openLocation(locationMap);
    },

    // 关闭地址弹窗
    onCloseAddress() {
      this.setData({
        showAddress: false
      });
    },

    // ComCard 组件事件处理
    onCardAction(e) {
      const {type, data, index} = e.detail;
      let that = this;

      preventActive(this, async () => {
        switch (type) {
          case 'collect':
            // 收藏企业
            const comDetail = {
              ...data
            };
            collect(that, comDetail, 'requestData', index);
            break;
          case 'relation':
            // 联系方式
            this.setData({
              activeEntId: data.ent_id,
              showContact: true
            });
            break;
          case 'official':
            // 官网跳转
            if (data.official_website) {
              wx.navigateTo({
                url: `/webview/webview?url=${encodeURIComponent(
                  data.official_website
                )}`
              });
            }
            break;
          case 'site':
            // 地址查看
            if (data.location && data.location.lat && data.location.lon) {
              this.setData({
                location: {
                  lat: +data.location.lat,
                  lon: +data.location.lon
                },
                locationTxt: data.register_address,
                addmarkers: [
                  {
                    id: 1,
                    latitude: +data.location.lat,
                    longitude: +data.location.lon,
                    iconPath:
                      'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png',
                    width: 20,
                    height: 20
                  }
                ],
                showAddress: true,
                locationMap: {
                  latitude: +data.location.lat, // 维度
                  longitude: +data.location.lon, // 经度
                  name: data.register_address, // 目的地定位名称
                  scale: 15, // 缩放比例
                  address: data.register_address // 导航详细地址
                }
              });
            } else {
              wx.showToast({
                title: '暂无地址信息',
                icon: 'none'
              });
            }
            break;
        }
      });
    },

    // 点击企业标题
    handleTit(e) {
      const {ent_id} = e.detail;
      if (ent_id) {
        wx.navigateTo({
          url: `/companyPackage/pages/companyDetail/companyDetail?entId=${ent_id}`
        });
      }
    },

    // 动态获取蒙层高度
    handleHeight() {
      const that = this;

      // 如果是弹窗模式，使用不同的计算方式
      if (this.properties.popupMode) {
        this.calculatePopupHeight();
      } else {
        // 普通模式的高度计算
        getHeight(that, '.business-list-component .page_head', data => {
          this.setData({
            computedHeight: data.screeHeight - data.res[0].height,
            isLogin: app.isLogin()
          });
        });
      }
    },
    // 判断是否用了原生导航栏

    // 弹窗模式下的高度计算
    calculatePopupHeight() {
      // 延迟执行，确保DOM渲染完成
      wx.nextTick(() => {
        const query = wx.createSelectorQuery().in(this);
        // 获取多个元素的高度
        query.select('.card-box').boundingClientRect(); // 组件容器高度
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        query.exec(res => {
          try {
            const systemInfo = wx.getSystemInfoSync();
            // 这个列表会有没有导航栏使用的情况 高度应该取screeHeight
            const windowHeight = Object.keys(currentPage.data).includes(
              'bottomTabs1'
            )
              ? systemInfo.screenHeight
              : systemInfo.windowHeight;
            this.setData({
              computedHeight: windowHeight - res[0].top,
              isLogin: app.isLogin()
            });
          } catch (error) {
            console.error('弹窗模式高度计算失败:', error);
            this.setFallbackHeight();
          }
        });
      });
    },
    // 设置备用高度
    setFallbackHeight() {
      try {
        const systemInfo = wx.getSystemInfoSync();
        const fallbackHeight = Math.max(300, systemInfo.screenHeight * 0.6);

        this.setData({
          computedHeight: fallbackHeight,
          isLogin: app.isLogin()
        });
      } catch (error) {
        // 最后的备用方案
        this.setData({
          computedHeight: 400,
          isLogin: app.isLogin()
        });
      }
    },

    // 顶部筛选--------这里逻辑有点问题
    onFlitter(e) {
      let {dropDownMenuTitle, externalFilterState, category, isIndustryMap} =
        this.data;
      const obj = e.detail;
      let tempRequestParams = {};

      // 更新下拉菜单标题
      if (obj.name1) dropDownMenuTitle[0] = obj.name1;
      if (obj.name2) dropDownMenuTitle[1] = obj.name2;
      // 清理临时字段并合并参数
      const {
        name1,
        name2,
        isFilter,
        regionData,
        industrial_list, //这里返回的是字符串
        area_code_list,
        chainType,
        ...filterParams
      } = obj;
      tempRequestParams = {
        ...filterParams
      };
      if (chainType === 'classic') {
        // 经典用这个字段
        tempRequestParams.classic_industry_code_list = industrial_list
          ? [industrial_list]
          : [];
      } else if (['chainMap', 'hot'].includes(chainType)) {
        tempRequestParams.industrial_list = industrial_list
          ? [industrial_list]
          : [];
      } else if (
        // 特殊情况_产业图谱 热点 如果没选 一进来有值得情况
        (isIndustryMap || category === 'hot' || category === 'chainMap') &&
        externalFilterState?.industrial_list?.code
      ) {
        tempRequestParams.industrial_list = [
          externalFilterState.industrial_list.code
        ];
      } else if (
        category === 'classic' &&
        externalFilterState?.classic_industry_code_list?.code
      ) {
        // 经典
        tempRequestParams.classic_industry_code_list = [
          externalFilterState.classic_industry_code_list.code
        ];
      }

      tempRequestParams.area_code_list =
        area_code_list && area_code_list !== 'All' ? [area_code_list] : [];

      this.setData(
        {
          requestParams: tempRequestParams,
          dropDownMenuTitle
        },
        () => {
          // 触发组件重新加载数据
          this.selectComponent('#refresh-scroll').reload();
        }
      );
    },

    // VIP弹窗
    vipPop(val) {
      if (val?.type === 'close') {
        this.setData({
          vipVisible: false
        });
        return;
      }
      this.setData({
        vipVisible: val
      });
    },

    // 应用外部筛选状态（用于回显外部的筛选条件）
    applyExternalFilterState(externalState) {
      if (!externalState) return;

      // console.log('BusinessListComponent 应用外部筛选状态:', externalState);

      const {
        regionData,
        industrial_list,
        filterParams,
        classic_industry_code_list,
        originalCode, // 产业链图谱 进来-跳转图谱取最原始的（排除子节点）
        originalName
      } = externalState;
      let {requestParams, dropDownMenuTitle, dropDownMenuConfig} = this.data;

      // 处理地区数据
      if (regionData && regionData.code && regionData.code !== 'All') {
        dropDownMenuTitle[0] = regionData.name || '全国';
        requestParams.area_code_list = [regionData.code];
      }

      // 处理热点和图谱产业链数据
      if (industrial_list && industrial_list.code) {
        const chainItem = industrial_list;
        if (chainItem && chainItem.code) {
          requestParams.industrial_list = [chainItem.code];
        }
        // 如果是产业链图谱 这个不显示
        if (
          dropDownMenuTitle.length !== 1 &&
          dropDownMenuConfig.includes('industry')
        ) {
          dropDownMenuTitle[1] = chainItem.name || '产业类型';
        }
      }
      // 处理经典产业链数据
      if (classic_industry_code_list && classic_industry_code_list.code) {
        const chainItem = classic_industry_code_list;
        if (chainItem && chainItem.code) {
          requestParams.classic_industry_code_list = [chainItem.code];
          if (
            dropDownMenuTitle.length !== 1 &&
            dropDownMenuConfig.includes('industry')
          ) {
            dropDownMenuTitle[1] = chainItem.name || '产业类型';
          }
        }
      }

      // 处理其他筛选参数（如果有的话）
      if (filterParams) {
        // 排除一些不需要的字段
        const {name1, name2, ...otherParams} = filterParams;
        requestParams = {
          ...requestParams,
          ...otherParams
        };
      }
      if (originalCode) {
        delete externalState.originalCode;
        delete externalState.originalName;
      }
      // console.log('333333', externalState);
      // 更新组件状态，包括传递给DropDownMenu的外部状态
      this.setData({
        requestParams,
        dropDownMenuTitle,
        externalFilterState: externalState,
        originalCode,
        originalName
      });
    },
    // 地图模式
    goMapMode() {
      const {
        isIndustryMap,
        externalFilterState,
        category,
        originalCode,
        originalName
      } = this.data;
      const name =
        category !== 'classic'
          ? externalFilterState?.industrial_list?.name
          : externalFilterState?.classic_industry_code_list?.name;

      const code =
        category !== 'classic'
          ? externalFilterState?.industrial_list?.code
          : externalFilterState?.classic_industry_code_list?.code;
      if (!this.data.purchased) {
        this.onNotPurchased();
        return;
      }
      let routeStr = `/industryPackage/pages/businessMapList/index?category=${category}`;
      if (isIndustryMap) {
        // 这是父亲节点 传递进去获取下拉弹窗
        routeStr = routeStr + `&isIndustryMap=true`;
      }
      if (originalCode) {
        routeStr =
          routeStr + `&chain_code=${originalCode}&chain_name=${originalName}`;
      } else {
        routeStr = routeStr + `&chain_code=${code}&chain_name=${name}`;
      }
      app.route(this, routeStr);
    },
    // 下拉筛选未购买触发的方法
    onNotPurchased() {
      this.setData({
        masonryVipVisible: true
      });
    },

    onVipClose() {
      this.setData({
        masonryVipVisible: false
      });
    },
    onSend() {
      this.onVipClose();
      // app.showToast(
      //   '您的申请已收到，稍后会有专属顾问联系您，请保持电话畅通，谢谢'
      // );
      Toast({
        message: '您的申请已收到，稍后会有专属顾问联系您，请保持电话畅通，谢谢',
        duration: 3000,
        selector: '#van-toast'
      });
    },

    // 联系方式弹窗关闭
    onContactClose() {
      this.setData({
        showContact: false
      });
    },

    // 监听滚动事件（来自refresh-scroll组件）
    onScrollEvent(e) {
      // 这个方法可以用来监听滚动，但refresh-scroll组件没有直接提供滚动事件
      // 我们通过监听scrolltolower事件来检测是否到底部
    },

    // 监听滚动到底部事件
    onScrollToLower(e) {
      const {purchased, requestData} = this.data;
      const {currentListLength} = e.detail || {};

      console.log('滚动到底部事件触发:', {
        purchased,
        requestDataLength: requestData.length,
        currentListLength
      });

      // 如果未购买且有数据（达到10条），说明滚动到了底部
      if (!purchased && requestData.length >= 10) {
        console.log('设置滚动到底部状态为 true，直接显示弹窗');
        this.setData({
          isScrolledToBottom: true,
          showBottomVipPopup: true // 直接设置弹窗显示
        });
        return;
      }

      // 如果已购买，正常处理滚动到底部
      if (purchased) {
        this.setData({
          isScrolledToBottom: false, // 已购买用户不需要显示VIP弹窗
          showBottomVipPopup: false
        });
      }
    },

    // 监听滚动事件（用于检测是否离开底部）
    onScroll(e) {
      const {purchased, requestData, showBottomVipPopup} = this.data;
      if (purchased || requestData.length < 10) return; // 已购买或数据不足10条不需要处理

      // 通过scroll-view的滚动事件来检测是否在底部
      const {scrollTop, scrollHeight, clientHeight} = e.detail;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 50; // 50px的容差

      console.log('滚动事件:', {
        scrollTop,
        scrollHeight,
        clientHeight,
        isAtBottom,
        showBottomVipPopup
      });

      // 如果当前显示弹窗但不在底部，隐藏弹窗
      if (showBottomVipPopup && !isAtBottom) {
        console.log('离开底部，隐藏弹窗');
        this.setData({
          isScrolledToBottom: false,
          showBottomVipPopup: false
        });
      }
    },

    // 更新底部VIP弹窗显示状态
    updateBottomVipPopupState() {
      const {purchased, isScrolledToBottom, requestData} = this.data;

      // 只有在未购买、有数据（10条）、且滚动到底部时才显示弹窗
      const shouldShowBottomVipPopup =
        !purchased && requestData.length >= 10 && isScrolledToBottom;

      console.log('更新底部VIP弹窗状态:', {
        purchased,
        isScrolledToBottom,
        requestDataLength: requestData.length,
        shouldShowBottomVipPopup
      });

      this.setData({
        showBottomVipPopup: shouldShowBottomVipPopup
      });
    }
  }
});
