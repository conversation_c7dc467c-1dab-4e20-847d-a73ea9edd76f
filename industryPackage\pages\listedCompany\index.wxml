<!--industryPackage/pages/businessMapList/index.wxml-->
<view class="pages">
  <view class="page_head">
    <DropDownMenu
      dropDownMenuTitle="{{dropDownMenuTitle}}"
      dropDownMenuConfig="{{dropDownMenuConfig}}"
      class="drop-menu"
      bindsubmit="onFlitter"
      bindvip="vipPop"
      isIndustryMap="{{isIndustryMap}}"
      externalFilterState="{{externalFilterState}}"
      useIndustrySingleSelect="{{isIndustryMap}}"
      chain_code="{{filter_code}}"
      id="drop-menu"
    />
  </view>

  <!-- -->
  <scroll-view scroll-y class="card-box" style="height: {{computedHeight}}px;">
    <!-- 数量 处理fixTabs -->
    <view class="fix-tabs-container {{fixTabsAnimated ? 'animated' : ''}}">
      <view class="fix-tabs-grid">
        <view
          class="fix-tab-item {{fixTabsAnimated ? 'item-animated' : 'item-hidden'}}"
          style="animation-delay: {{index * 100}}ms;"
          wx:for="{{fixTabs}}"
          wx:key="index"
          data-index="{{index}}"
          bindtap="onFixTabClick"
          hover-class="item-hover"
          hover-stay-time="150"
          hover-start-time="0"
        >
          <image
            src="{{item.bgImg}}.png"
            class="bg-image"
            mode="aspectFill"
          ></image>
          <view class="tab-content">
            <view class="tab-value">
              {{item.value}}<text class="tab-unit">{{item.unit}}</text>
            </view>
            <view class="tab-label">{{item.label}}</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 增长情况 -->
    <TitLayout isLine title="增长情况">
      <view slot="content">
        <echarts-bar chartData="{{trendData}}"> </echarts-bar>
      </view>
    </TitLayout>
    <!-- 区域排行 -->
    <TitLayout isLine title="区域排行">
      <view slot="content">
        <EchartsHorizontalBar
          chartData="{{horizontalBarData}}"
        ></EchartsHorizontalBar>
      </view>
    </TitLayout>
    <!-- 产业链分布 -->
    <TitLayout title="产业链分布" isLine type="masonry">
      <view slot="left" class="tu_left" bindtap="onTuClick">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_tree_scall.png"
          class="img"
        ></image>
        全屏
      </view>
      <view slot="content">
        <HorizontalTree treeData="{{treeData}}" />
      </view>
    </TitLayout>
    <view style="height: 90rpx; border: 1px solid transparent;"></view>
  </scroll-view>
  <!-- 浮窗-企业列表 -->
  <view class="fixed_btn" bindtap="onShowEnterpriseList">企业列表</view>
  <!-- 弹出企业列表 -->
  <van-popup
    show="{{ showPop }}"
    bind:close="onClose"
    position="right"
    custom-style="height: 100vh; width: 90%;"
  >
    <view class="popup-content" catchtouchmove="onPopupTouchMove">
      <!-- 关闭按钮放在弹窗内部 - 有内部弹窗时添加蒙层效果 -->
      <view
        class="popup-close-btn {{hasActivePopup ? 'under-mask' : ''}}"
        catchtap="onClose"
      >
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/close.png"
        />
      </view>
      <!-- 企业列表组件 - 使用wx:if确保每次都是新实例 -->
      <BusinessListComponent
        wx:if="{{showPop}}"
        dropDownMenuConfig="{{['region', 'filter']}}"
        fixedTitle="{{popupFixedTitle}}"
        showMapMode="{{false}}"
        popupMode="{{true}}"
        externalFilterState="{{externalFilterState}}"
        isIndustryMap="{{isIndustryMap}}"
        dropDownMenuTitle="{{['全国', '更多筛选']}}"
        bindpopupStateChange="onPopupStateChange"
      />
    </view>
  </van-popup>
  <!-- VIP弹窗 -->
  <VipPop visible="{{vipVisible}}" bindclose="vipPop"></VipPop>
</view>
