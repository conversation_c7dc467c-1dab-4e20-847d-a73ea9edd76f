/* industryPackage/components/SingleSelect/index.scss */

/* 主容器样式 */
.area {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Tab 头部样式 */
.tab-header {
  position: relative;
  display: flex;
  background: #fff;
  height: 84rpx;
  z-index: 1;
  box-sizing: border-box;
  width: 100%;
  // 1rpx下边框
  &::before {
    content: " ";
    width: 100%;
    height: 1rpx;
    background: #eee;
    position: absolute;
    bottom: 0;
    left: 0;
    // transform: scaleY(0.5);
  }
  &::after {
    content: " ";
    width: 100%;
    height: 1rpx;
    background: #eee;
    position: absolute;
    top: 0;
    left: 0;
    // transform: scaleY(0.5);
  }
}

.tab-item {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  .tab-text {
    font-size: 28rpx;
    color: #74798c;
    transition: all 0.3s ease;
    font-weight: 600;
  }
  &.active .tab-text {
    font-weight: 600;
    font-size: 28rpx;
    color: #e72410;
  }
  .tab-line {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40rpx;
    height: 6rpx;
    background: linear-gradient(90deg, #e72410 0%, #f17b6f 100%);
    opacity: 0;
    transition: opacity 0.3s ease;

    &.show {
      opacity: 1;
    }
  }
}

/* 内容容器样式 */
.select-container {
  width: 100%;
  position: relative;
  overflow: hidden;
  min-height: 400rpx;
}

.select-wrap {
  height: 100%;
  width: 100%;
  display: flex;
  font-weight: 400;
  font-size: 26rpx;
  color: #525665;
  min-height: 400rpx;
}

/* 动态多级列表样式 */
.dynamic-levels {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden; // 不支持水平滚动，直接挤压
  font-weight: 400;
  font-size: 26rpx;
  color: #525665;
}

.level-list {
  height: 100%;
  flex-shrink: 0; // 防止被压缩
  position: relative;

  // 右边框分隔线（除了最后一个）
  &:not(:last-child)::after {
    content: " ";
    position: absolute;
    right: 0;
    top: 0;
    width: 1rpx;
    height: 100%;
    background: #eee;
    // transform: scaleX(0.5);
  }

  // 第一级保留原有样式
  &.first-level {
    background: #f7f7f7;
    &::after {
      background: transparent;
    }

    .item.active {
      background-color: #fff;
    }

    .item.selected {
      background-color: #fff;
      font-weight: 600;
      font-size: 26rpx;
      color: #e72410;
    }
  }

  // 其他级别使用白色背景
  &:not(.first-level) {
    background-color: #fff;

    .item.selected {
      font-weight: 600;
      font-size: 26rpx;
      color: #e72410;
    }
  }

  // 每个级别的项目样式
  .item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx; /* 31rpx/750rpx=4.13%, 35rpx/750rpx=4.67% */
    box-sizing: border-box;
    position: relative;
    height: 88rpx;
    text {
      font-weight: 400;
      font-size: 26rpx;
      color: #525665;
    }
  }
}

.selected-text {
  color: #e72410 !important;
  font-size: 26rpx !important;
  font-weight: 600 !important;
}

.checkmark {
  width: 28rpx; /* 28rpx / 750rpx = 3.73% */
  height: 28rpx; /* 高度保持固定，避免变形 */
  flex-shrink: 0; /* 防止图标被压缩 */
  margin-left: 24rpx !important;
}

.checkmark.show {
  opacity: 1;
}

.placeholder-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
}
