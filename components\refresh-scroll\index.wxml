<!-- 简化版下拉刷新和上拉加载更多组件 -->
<view class="refresh-scroll-wrapper custom-wrapper-class">
  <!-- 初始加载状态 -->
  <view
    wx:if="{{isInitialLoading}}"
    class="initial-loading-container custom-loading-class"
    style="height: {{containerHeight}}px;"
  >
    <view class="initial-loading-content">
      <view class="loading-spinner">
        <!-- 条状加载动画 -->
        <view class="spinner">
          <view class="rect1 kuai"></view>
          <view class="rect2 kuai"></view>
          <view class="rect3 kuai"></view>
          <view class="rect4 kuai"></view>
          <view class="rect5 kuai"></view>
        </view>
      </view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <scroll-view
    wx:else
    class="refresh-scroll-container custom-container-class"
    style="height: {{containerHeight}}px; background: #f7f7f7; padding-bottom: 20rpx;"
    scroll-y="{{true}}"
    refresher-enabled="{{true}}"
    refresher-triggered="{{isTriggered}}"
    refresher-background="#f7f7f7"
    lower-threshold="50"
    bindrefresherrefresh="onRefresh"
    bindscrolltolower="onScrollToLower"
    bindscroll="onScroll"
  >
    <!-- 外部内容插槽 -->
    <slot name="content"></slot>

    <!-- 加载更多区域 -->
    <view wx:if="{{list.length > 0}}" class="load-more-container">
      <view wx:if="{{hasMore && !isLoading}}" class="load-more-trigger">
        <text class="load-more-text">上拉加载更多</text>
      </view>

      <view wx:elif="{{isLoading}}" class="loading-more">
        <text class="loading-text">加载中...</text>
      </view>

      <view wx:else class="no-more">
        <text class="no-more-text">没有更多数据了</text>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{isEmpty}}" class="empty-container custom-empty-class">
      <view class="default-empty">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/null.png"
          class="empty-icon"
          mode="aspectFit"
        />
        <text class="empty-text">{{emptyText}}</text>
        <text class="empty-tip">{{emptyTip}}</text>
      </view>
    </view>
  </scroll-view>
</view>
