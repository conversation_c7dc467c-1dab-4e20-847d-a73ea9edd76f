import {getHeight} from '../../../utils/height';
import {hasPrivile} from '../../../utils/route';
import {
  pastMonthSearchApi,
  hotIndustrySimpleListApi,
  classicIndustrySimpleListApi,
  originalIndustrySimpleListApi,
  userBrowseHistoryApi,
  cleanBrowseHistoryApi
} from '../../../service/industryApi';

const app = getApp();

Page({
  data: {
    inputVal: '', // 搜索值
    searchRes: true, // 搜索结果
    loading: false,
    currentFilter: {
      chain_code: 'all'
    }, // 当前筛选条件
    scrollHeight: 0, // 页面高度
    recentViewedList: [], // 最近查看列表
    hot_search_industry_list: [],
    original_industry_list: [], //热门产业领域
    vipVisible: false // VIP弹窗显示状态
  },
  onLoad: async function (options) {
    let {
      filter_code,
      keyword = '',
      isFilter
      // chian_name后续跳转这个页面有可能带参数 预留
    } = options;
    this.scrollH();
    keyword = decodeURIComponent(keyword);

    this.setData(
      {
        currentFilter: {
          chain_code: filter_code
        },
        inputVal: keyword
      },
      () => {
        keyword &&
          this.onInput({
            detail: {
              keyword,
              type: 'confirm',
              filter: this.data.currentFilter
            }
          });
        if (isFilter) {
          setTimeout(() => {
            this.selectComponent('#myChild').setVisible();
          }, 1000);
        }
      }
    );
  },
  onShow() {
    this.getRankData();
  },

  // 搜索组件事件处理
  onInput(e) {
    const {inputVal, filter, type} = e.detail;
    switch (type) {
      case 'filterChange':
      case 'confirm':
      case 'onBlur':
        if (this.data.inputVal == inputVal && this.data.currentFilter == filter)
          return;
        this.setData(
          {
            currentFilter: filter,
            inputVal,
            loading: !!String(inputVal || '').trim()
          },
          () => {
            this.performSearch(inputVal, filter);
          }
        );
        break;

      default:
        break;
    }

    return;
  },

  // 执行搜索
  async performSearch(keyword, filter = null) {
    if (!keyword) {
      this.setData({
        searchRes: false,
        loading: false
      });
      return;
    }
    app.showLoading('加载中');
    const res = await Promise.all([
      hotIndustrySimpleListApi({
        keyword
      }),
      classicIndustrySimpleListApi({
        keyword: keyword
      }),
      originalIndustrySimpleListApi({
        keyword: keyword,
        size: 200
      })
    ]);
    let searchRes = false;
    if (
      filter.chain_code === 'all' &&
      (res[0]?.list?.length || res[1]?.list?.length || res[2]?.list?.length)
    ) {
      searchRes = true;
    } else if (filter.chain_code === 'hot' && res[0]?.list?.length) {
      searchRes = true;
    } else if (filter.chain_code === 'classic' && res[1]?.list?.length) {
      searchRes = true;
    } else if (filter.chain_code === 'chainMap' && res[2]?.list?.length) {
      searchRes = true;
    }

    this.setData(
      {
        searchRes,
        loading: false,
        hotCardList: res[0].list.map(i => ({
          id: i.chain_code,
          title: i.chain_name,
          fieldCount: i.chain_node_count,
          companyCount: i.enterprise_amount,
          purchased: i.purchased,
          back_count: i.enterprise_add_amount,
          tag: i.cluster_name || '',
          types: 'hot'
        })),
        classicCardList: res[1].list.map(i => ({
          id: i.classic_industrial_id,
          title: i.classic_industrial_name,
          fieldCount: i.node_count,
          companyCount: i.enterprise_count,
          tag: i.cluster_name || '',
          back_count: i.enterprise_back_count,
          types: 'classic'
        })),
        originalCardList: res[2].list.map(i => ({
          id: i.chain_code,
          title: i.chain_name,
          fieldCount: i.chain_node_count,
          companyCount: i.enterprise_amount,
          purchased: i.purchased,
          back_count: i.enterprise_add_amount,
          types: 'chainMap' // 标识
        }))
      },
      () => {
        wx.hideLoading();
      }
    );
  },

  // 计算页面高度
  scrollH() {
    getHeight(this, ['.search-container'], data => {
      const {screeHeight, res} = data;
      const searchHeight = res[0]?.height; // 默认搜索组件高度
      const scrollHeight = screeHeight - searchHeight;
      this.setData({
        scrollHeight
      });
    });
  },
  // 前十排行榜
  async getRankData() {
    app.showLoading('加载中');
    // 排行
    const {hot_search_industry_list, original_industry_list} =
      await pastMonthSearchApi();
    await this.getHistoryApi();
    // console.log(res);
    // 最近查看
    const dataMap = {
      HOT_INDUSTRY: '热点产业领域',
      CLASSIC_INDUSTRY: '经典产业领域',
      ORIGINAL_INDUSTRY: '产业链图谱'
    };
    this.setData(
      {
        hot_search_industry_list: hot_search_industry_list.map(i => ({
          ...i,
          model_type_string: dataMap[i.model_type]
        })),
        original_industry_list
      },
      () => {
        wx.hideLoading();
      }
    );
  },
  async getHistoryApi() {
    const recentViewedList = await userBrowseHistoryApi();
    this.setData({
      recentViewedList: recentViewedList.map(item => ({
        chain_code: item.relation_key,
        chain_name: item.relation_name,
        model_type: item.model_type
      }))
    });
  },
  // 前十排行榜跳转 todo 后端到时候需要加上编码 code 以及是否购买
  async goDetail(e) {
    const {
      item: {name, relation_key, model_type}
    } = e.detail;

    let url = '';
    let type =
      model_type == 'CLASSIC_INDUSTRY'
        ? 'classic'
        : model_type === 'HOT_INDUSTRY'
        ? 'hot'
        : 'chainMap';
    if (model_type == 'ORIGINAL_INDUSTRY') {
      url = `/industryPackage/pages/IndustryListMasonry/index?chain_name=${encodeURIComponent(
        name
      )}&chain_code=${relation_key}`;
    } else {
      url = `/industryPackage/pages/IndustryListVip/index?chain_code=${relation_key}&chain_name=${encodeURIComponent(
        name
      )}&category=${type}`;
    }

    if (type !== 'chainMap') {
      let bol = await this._checkVipStatus();
      if (!bol) return;
    }
    app.route(this, url);
  },

  // 最近查看项点击事件
  async onRecentItemClick(e) {
    const that = this;
    const {item, type} = e.detail;
    const fixedUrl3 = '/industryPackage/pages/IndustryListMasonry/index',
      fixedUrl4 = '/industryPackage/pages/IndustryListVip/index'; // 产业名单vip
    let url = '';
    switch (type) {
      case 'recentClear':
        // 清空最近查看
        console.log(2222);

        wx.showModal({
          title: '删除查看',
          content: '确定要删除最近查看?',
          success: async res => {
            if (res.confirm) {
              await cleanBrowseHistoryApi();
              await that.getHistoryApi();
            }
          }
        });

        break;
      case 'recent':
        const {model_type, chain_code, chain_name} = item;
        if (model_type === 'ORIGINAL_INDUSTRY') {
          // 产业链图谱
          url = `${fixedUrl3}?chain_code=${chain_code}&chain_name=${encodeURIComponent(
            chain_name
          )}`;
          app.route(this, url);
        } else {
          const category =
            model_type === 'CLASSIC_INDUSTRY' ? 'classic' : 'hot';
          url = `${fixedUrl4}?chain_code=${chain_code}&chain_name=${encodeURIComponent(
            chain_name
          )}&category=${category}`;
          const bol = await this._checkVipStatus();
          if (!bol) return;
          app.route(this, url);
        }

        break;
    }
  },
  async handleClick(e) {
    const {item} = e.detail;
    const {types} = item;
    const {title, purchased, id} = item;

    let url = '';
    // 热门 经典待补充
    if (types === 'chainMap') {
      // 产业链图谱
      url = `/industryPackage/pages/IndustryListMasonry/index?chain_name=${encodeURIComponent(
        title
      )}&chain_code=${id}&purchased=${purchased}`;
      app.route(this, url);
    } else {
      url = `/industryPackage/pages/IndustryListVip/index?chain_code=${id}&chain_name=${encodeURIComponent(
        title
      )}&category=${types}`;
      const bol = await this._checkVipStatus();
      if (!bol) return;
      app.route(this, url);
    }
  },
  vipPop(val) {
    if (val?.type === 'close') {
      this.setData({
        vipVisible: false
      });
      return;
    }
    this.setData({
      vipVisible: val
    });
  },
  async _checkVipStatus() {
    let str = await hasPrivile({
      packageType: true
    });
    if (str == '游客') {
      app.route(this, '/pages/login/login');
      return false;
    } else if (str == '普通VIP') {
      this.setData({
        vipVisible: true
      });
      return false;
    }
    return true;
  }
});
