<view wx:if="{{recentList.length}}" class="recent-viewed">
  <view class="his_titles">
    <text class="his_title_l">最近查看</text>
    <view class="his_title_icon" bindtap="onItemClick" data-type="recentClear">
      <image
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/in_delet.png"
        mode="aspectFit"
      ></image>
    </view>
  </view>
  <view class="recent-item-wrap">
    <view
      class="recent-item {{item.model_type=='ORIGINAL_INDUSTRY' ? 'highlight' : '' }}"
      wx:for="{{recentList}}"
      wx:key="{{keyField}}"
      data-item="{{item}}"
      bindtap="onItemClick"
      data-type="recent"
    >
      {{item[nameField]}}
    </view>
  </view>
</view>
