<!--industryPackage/components/SingleSelect/index.wxml-->
<HalfScreenPop
  showCloseBtn="{{false}}"
  visible="{{visible}}"
  position="bottom"
  bindclose="close"
  bindsubmit="submit"
  startDistance="{{startDistance}}"
  disableAnimation="{{true}}"
  _maskClosable="{{true}}"
  zIndex="{{zIndex}}"
  showFooter="{{false}}"
>
  <view slot="customhead">
    <view class="pop-name">
      选择城市
      <image
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/in_close.png"
        class="img_close"
        bindtap="close"
      />
    </view>
  </view>
  <view
    slot="customContent"
    class="area"
    style="margin-top:{{top || 0}}px; min-height: 760rpx;"
  >
    <!-- 内容容器 -->
    <view class="select-container">
      <!-- 占位内容 -->
      <view wx:if="{{!readyToShow}}" class="placeholder-content">
        <view class="placeholder-text">数据加载中...</view>
      </view>

      <!-- 主要内容 - 只有在准备好时才显示 -->
      <view wx:else class="select-wrap">
        <!-- 父级列表 -->
        <view class="list parent-list">
          <scroll-view scroll-y="true" style="height:100%">
            <view
              class="item parent {{item.active && 'active'}} {{item.selected && 'selected'}}"
              bindtap="selectParent"
              data-code="{{item.code}}"
              data-name="{{item.name}}"
              data-index="{{index}}"
              wx:for="{{parentList}}"
              wx:key="code"
            >
              <text
                class="{{item.selected && 'selected-text'}}"
                >{{item.name}}</text
              >
              <!-- 直辖市显示勾选图标 -->
              <image
                class="checkmark {{(tempSelection.parent && tempSelection.parent.code === item.code && !tempSelection.child) && 'show'}}"
                wx:if="{{tempSelection.parent && tempSelection.parent.code === item.code && !tempSelection.child}}"
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_checked.png"
              ></image>
            </view>
          </scroll-view>
        </view>

        <!-- 子级列表 -->
        <view class="list child-list">
          <scroll-view scroll-y="true" style="height:100%">
            <view
              class="item child {{item.selected && 'selected'}}"
              bindtap="selectChild"
              data-code="{{item.code}}"
              data-name="{{item.name}}"
              data-lock="{{item.lock}}"
              wx:for="{{activeChildList}}"
              wx:key="code"
            >
              <text
                class="{{item.selected && 'selected-text'}}"
                >{{item.name}}</text
              >
              <image
                class="checkmark {{item.selected && 'show'}}"
                wx:if="{{item.selected}}"
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_checked.png"
              ></image>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <!-- 自定义底部按钮 -->
    <view class="custom-footer">
      <button class="cancel-btn" bindtap="close">取消</button>
      <button
        class="confirm-btn {{!tempSelection.isValid && 'disabled'}}"
        bindtap="submit"
        disabled="{{!tempSelection.isValid}}"
      >
        确定
      </button>
    </view>
  </view>
</HalfScreenPop>
